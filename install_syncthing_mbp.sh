#!/usr/bin/env bash
set -euo pipefail

# Install (or reinstall) Syncthing on MBP via Homebrew
# Usage: ./install_syncthing_mbp.sh

echo "🚀 Syncthing installation on MBP"
echo "================================="

# Check if Homebrew is installed
if ! command -v brew >/dev/null 2>&1; then
    echo "❌ Homebrew not found. Please install Homebrew first:"
    echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
fi

echo "📦 Checking current Syncthing installation..."

# Check if Syncthing is already installed
if brew list syncthing >/dev/null 2>&1; then
    CURRENT_VERSION=$(brew list --versions syncthing | head -n1)
    echo "✅ Syncthing already installed: $CURRENT_VERSION"
    
    # Ask if user wants to upgrade
    echo "🔄 Checking for updates..."
    if brew outdated syncthing >/dev/null 2>&1; then
        echo "📈 Update available. Upgrading Syncthing..."
        brew upgrade syncthing
    else
        echo "✅ Syncthing is up to date"
    fi
else
    echo "📦 Installing Syncthing via Homebrew..."
    brew install syncthing
fi

# V
SYNCTHING_PATH=$(brew --prefix)/bin/syncthing
if [[ ! -f "$SYNCTHING_PATH" ]]; then
    # Try the Syncthing-specific Homebrew prefix as fallback
    SYNCTHING_PATH="$(brew --prefix syncthing)/bin/syncthing"
fi

if [[ -f "$SYNCTHING_PATH" ]]; then
    echo "✅ Syncthing installed successfully at: $SYNCTHING_PATH"
    echo "📋 Version: $($SYNCTHING_PATH --version | head -n1)"
else
    echo "❌ Syncthing installation failed or binary not found"
    exit 1
fi

# Create config directory
echo "📁 Creating config directory..."
mkdir -p ~/.config/syncthing

# Check PATH configuration
echo "🔍 Checking PATH configuration..."
if command -v syncthing >/dev/null 2>&1; then
    echo "✅ Syncthing is in PATH: $(which syncthing)"
else
    echo "⚠️  Syncthing not in PATH. You may need to add $(brew --prefix)/bin to your PATH"
    echo "   Add this to your shell profile (~/.zshrc or ~/.bash_profile):"
    echo "   export PATH=\"$(brew --prefix)/bin:\$PATH\""
fi

# Stop any existing Syncthing processes
echo "⏹  Stopping any existing Syncthing processes..."
pkill -f syncthing 2>/dev/null || true
sleep 2

echo "🎉 MBP Syncthing installation complete!"
echo ""
echo "📋 Summary:"
echo "  Binary: $SYNCTHING_PATH"
echo "  Config: ~/.config/syncthing/"
echo "  Next steps: Run ./deploy-mbp.sh to deploy configuration"
