#!/usr/bin/env bash
set -euo pipefail

# Install (or reinstall) Syncthing on a remote Pi via SSH, overwriting any existing install.
# Usage: ./install_syncthing_pi.sh [pi-host]
# Default host: "pi"

PI_HOST="${1:-pi}"

echo "🚀 Remote Syncthing installation on $PI_HOST"
echo "========================================="

#

echo "➡️  SSH into $PI_HOST..."
ssh "$PI_HOST" 'bash -s' <<'REMOTE_SCRIPT'
set -euo pipefail

echo "[Pi] 📦 Preparing to (re)install Syncthing"


echo "[Pi] ⏹ Stopping existing Syncthing user service (if running)..."
systemctl --user stop syncthing 2>/dev/null || true

sleep 1
echo "[Pi] 🔪 Killing stray syncthing processes (if any)..."
pkill -f syncthing 2>/dev/null || true

if command -v syncthing >/dev/null 2>&1; then
	echo "[Pi] ✅ Syncthing already installed: $(syncthing --version | head -n1)"
	echo "[Pi] ⏭  Skipping package installation. (Remove it first if you want a fresh install.)"
else
	echo "[Pi] 🔄 Updating apt metadata..."
	sudo apt-get update -y -qq
	echo "[Pi] 📦 Installing syncthing package..."
	sudo apt-get install -y syncthing
fi

# Ensure lingering so user service can run without active login session
echo "[Pi] 🔐 Enabling linger for user $USER..."
sudo loginctl enable-linger "$USER" >/dev/null 2>&1 || true

mkdir -p ~/.config/syncthing


systemctl --user daemon-reload || true

echo "[Pi] ✅ Enabling + starting user service..."
systemctl --user enable syncthing.service || true
systemctl --user start syncthing.service || {
	echo "[Pi] ❌ Failed to start service via systemd user manager. Attempting foreground fallback..."
	nohup syncthing --no-browser --no-restart --logflags=0 > ~/.config/syncthing/syncthing.fallback.log 2>&1 &
	sleep 2
}

sleep 2

if systemctl --user is-active syncthing >/dev/null 2>&1; then
	echo "[Pi] 🎉 Syncthing running under systemd user service."
else
	if pgrep -f "syncthing" >/dev/null 2>&1; then
		echo "[Pi] ⚠️  Running under fallback background process (not systemd)."
	else
		echo "[Pi] ❌ Syncthing failed to start." >&2
		exit 1
	fi
fi

echo "[Pi] 🌐 Web UI: http://localhost:8384"
REMOTE_SCRIPT

echo "✅ Remote installation complete on $PI_HOST"
echo "You can access the web UI (from the Pi) at: http://localhost:8384"
